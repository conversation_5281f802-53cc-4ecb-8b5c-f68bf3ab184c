'use client';

import React, { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import ImageUploadWithURL from '../../../components/ImageUploadWithURL';

const TestImagesPage = () => {
  const [testImages, setTestImages] = useState<string[]>([]);
  const [dbData, setDbData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const fetchDatabaseData = async () => {
    setLoading(true);
    try {
      // جلب الفئات
      const categoriesResponse = await fetch('/api/admin/categories');
      const categoriesData = await categoriesResponse.json();
      
      // جلب الفئات الفرعية
      const subcategoriesResponse = await fetch('/api/admin/subcategories');
      const subcategoriesData = await subcategoriesResponse.json();
      
      // جلب المنتجات
      const productsResponse = await fetch('/api/admin/products');
      const productsData = await productsResponse.json();

      setDbData({
        categories: categoriesData.data || [],
        subcategories: subcategoriesData.data || [],
        products: productsData.data || []
      });
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDatabaseData();
  }, []);

  const testCreateCategory = async () => {
    if (testImages.length === 0) {
      alert('يرجى إضافة صورة أولاً');
      return;
    }

    try {
      const response = await fetch('/api/admin/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Test Category',
          nameAr: 'فئة تجريبية',
          description: 'Test category description',
          descriptionAr: 'وصف الفئة التجريبية',
          image: testImages[0],
          isActive: true
        }),
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إنشاء الفئة بنجاح!');
        fetchDatabaseData(); // تحديث البيانات
        setTestImages([]); // مسح الصور
      } else {
        alert('فشل في إنشاء الفئة: ' + result.messageAr);
      }
    } catch (error) {
      console.error('خطأ في إنشاء الفئة:', error);
      alert('حدث خطأ أثناء إنشاء الفئة');
    }
  };

  return (
    <AdminLayout title="اختبار الصور">
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">اختبار رفع الصور</h2>
          
          <ImageUploadWithURL
            onImagesUploaded={setTestImages}
            multiple={false}
            currentImages={testImages}
            label="اختبار رفع صورة"
          />

          {testImages.length > 0 && (
            <div className="mt-4">
              <button
                onClick={testCreateCategory}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
              >
                إنشاء فئة تجريبية بهذه الصورة
              </button>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">بيانات قاعدة البيانات</h2>
            <button
              onClick={fetchDatabaseData}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg"
            >
              {loading ? 'جاري التحديث...' : 'تحديث البيانات'}
            </button>
          </div>

          {dbData && (
            <div className="space-y-6">
              {/* الفئات الرئيسية */}
              <div>
                <h3 className="text-lg font-semibold mb-2">الفئات الرئيسية ({dbData.categories.length})</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {dbData.categories.slice(0, 6).map((category: any) => (
                    <div key={category.id} className="border rounded-lg p-3">
                      <h4 className="font-medium">{category.name_ar}</h4>
                      <p className="text-sm text-gray-600">{category.name}</p>
                      <div className="mt-2">
                        <span className="text-xs text-gray-500">الصورة: </span>
                        {category.image_url ? (
                          <div>
                            <span className="text-xs text-green-600">✓ موجودة</span>
                            <div className="mt-1">
                              <img
                                src={category.image_url}
                                alt={category.name_ar}
                                className="w-16 h-16 object-cover rounded"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = '/api/placeholder?width=64&height=64&text=خطأ';
                                }}
                              />
                            </div>
                            <p className="text-xs text-gray-400 mt-1 break-all">{category.image_url}</p>
                          </div>
                        ) : (
                          <span className="text-xs text-red-600">✗ غير موجودة</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* الفئات الفرعية */}
              <div>
                <h3 className="text-lg font-semibold mb-2">الفئات الفرعية ({dbData.subcategories.length})</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {dbData.subcategories.slice(0, 6).map((subcategory: any) => (
                    <div key={subcategory.id} className="border rounded-lg p-3">
                      <h4 className="font-medium">{subcategory.name_ar}</h4>
                      <p className="text-sm text-gray-600">{subcategory.name}</p>
                      <div className="mt-2">
                        <span className="text-xs text-gray-500">الصورة: </span>
                        {subcategory.image_url ? (
                          <div>
                            <span className="text-xs text-green-600">✓ موجودة</span>
                            <div className="mt-1">
                              <img
                                src={subcategory.image_url}
                                alt={subcategory.name_ar}
                                className="w-16 h-16 object-cover rounded"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = '/api/placeholder?width=64&height=64&text=خطأ';
                                }}
                              />
                            </div>
                            <p className="text-xs text-gray-400 mt-1 break-all">{subcategory.image_url}</p>
                          </div>
                        ) : (
                          <span className="text-xs text-red-600">✗ غير موجودة</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* المنتجات */}
              <div>
                <h3 className="text-lg font-semibold mb-2">المنتجات ({dbData.products.length})</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {dbData.products.slice(0, 6).map((product: any) => (
                    <div key={product.id} className="border rounded-lg p-3">
                      <h4 className="font-medium">{product.title_ar}</h4>
                      <p className="text-sm text-gray-600">{product.title}</p>
                      <div className="mt-2">
                        <span className="text-xs text-gray-500">الصور: </span>
                        {product.images && product.images.length > 0 ? (
                          <div>
                            <span className="text-xs text-green-600">✓ {product.images.length} صورة</span>
                            <div className="mt-1 flex flex-wrap gap-1">
                              {product.images.slice(0, 3).map((image: string, index: number) => (
                                <img
                                  key={index}
                                  src={image}
                                  alt={`${product.title_ar} ${index + 1}`}
                                  className="w-12 h-12 object-cover rounded"
                                  onError={(e) => {
                                    (e.target as HTMLImageElement).src = '/api/placeholder?width=48&height=48&text=خطأ';
                                  }}
                                />
                              ))}
                            </div>
                          </div>
                        ) : (
                          <span className="text-xs text-red-600">✗ لا توجد صور</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default TestImagesPage;
