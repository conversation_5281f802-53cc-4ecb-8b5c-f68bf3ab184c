const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// قراءة ملف .env.local
dotenv.config({ path: '.env.local' });

// إعدادات قاعدة البيانات - تؤخذ من .env.local
const dbConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+00:00',
  ssl: false
};

// دالة لاختبار الاتصال بقاعدة البيانات
async function testDatabaseConnection() {
  // التحقق من تحميل متغيرات البيئة
  if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_NAME) {
    console.error('❌ متغيرات قاعدة البيانات غير محملة من .env.local');
    console.error('تأكد من وجود الملف .env.local مع المتغيرات التالية:');
    console.error('DB_HOST, DB_USER, DB_PASSWORD, DB_NAME');
    return false;
  }
  console.log('🔄 Testing database connection...');
  console.log('📊 Database configuration:');
  console.log('   Host:', dbConfig.host);
  console.log('   Port:', dbConfig.port);
  console.log('   User:', dbConfig.user);
  console.log('   Database:', dbConfig.database);
  console.log('   Has Password:', !!dbConfig.password);
  console.log('');

  const configs = [
    // التكوين الأساسي
    {
      ...dbConfig,
      name: 'Standard connection'
    },
    // تكوين بديل بدون كلمة مرور
    {
      ...dbConfig,
      password: undefined,
      name: 'Connection without password'
    },
    // تكوين بديل مع unix socket
    {
      ...dbConfig,
      socketPath: '/var/run/mysqld/mysqld.sock',
      host: undefined,
      name: 'Unix socket connection'
    },
    // تكوين بديل مع localhost IP
    {
      ...dbConfig,
      host: '127.0.0.1',
      name: 'IP localhost connection'
    }
  ];

  let successfulConnection = null;

  for (let i = 0; i < configs.length; i++) {
    const config = configs[i];
    const configName = config.name;
    delete config.name; // إزالة الاسم من التكوين

    try {
      console.log(`🔄 Trying ${configName}...`);
      const connection = await mysql.createConnection(config);
      
      // اختبار الاتصال
      await connection.ping();
      console.log(`✅ ${configName} successful!`);
      
      // اختبار استعلام بسيط
      const [result] = await connection.execute('SELECT 1 as test');
      console.log(`✅ Query test successful:`, result[0]);
      
      // اختبار الجداول
      try {
        const [tables] = await connection.execute(`
          SELECT TABLE_NAME 
          FROM information_schema.tables 
          WHERE table_schema = ?
        `, [config.database]);
        console.log(`✅ Found ${tables.length} tables in database`);
        
        // عرض أسماء الجداول
        if (tables.length > 0) {
          console.log('📋 Tables:', tables.map(t => t.TABLE_NAME).join(', '));
        }
      } catch (tableError) {
        console.warn('⚠️ Could not list tables:', tableError.message);
      }
      
      await connection.end();
      successfulConnection = config;
      break;
      
    } catch (error) {
      console.log(`❌ ${configName} failed:`, error.message);
      if (error.code) {
        console.log(`   Error code: ${error.code}`);
      }
      if (error.errno) {
        console.log(`   Error number: ${error.errno}`);
      }
      console.log('');
    }
  }

  if (successfulConnection) {
    console.log('🎉 Database connection test completed successfully!');
    console.log('✅ The sitemap generation should work with this configuration.');
    return true;
  } else {
    console.log('❌ All connection attempts failed!');
    console.log('');
    console.log('🔧 Troubleshooting suggestions:');
    console.log('1. Check if MySQL service is running: systemctl status mysql');
    console.log('2. Check MySQL user permissions: mysql -u root -p');
    console.log('3. Verify database exists: SHOW DATABASES;');
    console.log('4. Check environment variables are set correctly');
    console.log('5. Try connecting with mysql client: mysql -u root -p droobhajer_db');
    return false;
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testDatabaseConnection()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testDatabaseConnection };
