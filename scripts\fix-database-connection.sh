#!/bin/bash

# 🔧 إصلاح مشاكل الاتصال بقاعدة البيانات
# مشروع دروب هاجر - DROOB HAJER

echo "🔧 إصلاح مشاكل الاتصال بقاعدة البيانات..."
echo "================================================"

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت!"
    exit 1
fi

# التحقق من وجود MySQL
if ! command -v mysql &> /dev/null; then
    echo "❌ MySQL غير مثبت!"
    exit 1
fi

echo "✅ Node.js و MySQL متوفران"
echo ""

# اختبار الاتصال الحالي
echo "🔄 اختبار الاتصال الحالي..."
if npm run test-db; then
    echo "✅ الاتصال يعمل بشكل صحيح!"
    echo "🎉 لا حاجة لإصلاحات إضافية"
    exit 0
fi

echo ""
echo "❌ فشل الاتصال - بدء عملية الإصلاح..."
echo ""

# التحقق من حالة MySQL
echo "🔄 فحص حالة MySQL..."
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL يعمل"
else
    echo "⚠️ MySQL متوقف - محاولة إعادة التشغيل..."
    sudo systemctl start mysql
    sleep 3
    if systemctl is-active --quiet mysql; then
        echo "✅ تم تشغيل MySQL بنجاح"
    else
        echo "❌ فشل في تشغيل MySQL"
        exit 1
    fi
fi

echo ""

# محاولة الاتصال بـ MySQL كـ root
echo "🔄 اختبار الاتصال بـ MySQL..."
if mysql -u root -e "SELECT 1;" &> /dev/null; then
    echo "✅ الاتصال بـ MySQL كـ root يعمل"
    
    # التحقق من وجود قاعدة البيانات
    echo "🔄 فحص قاعدة البيانات droobhajer_db..."
    if mysql -u root -e "USE droobhajer_db; SELECT 1;" &> /dev/null; then
        echo "✅ قاعدة البيانات droobhajer_db موجودة"
        
        # اختبار الاتصال مرة أخرى
        echo "🔄 اختبار الاتصال من Node.js..."
        if npm run test-db; then
            echo "🎉 تم إصلاح المشكلة!"
            echo "✅ يمكنك الآن تشغيل: npm run generate-sitemap"
            exit 0
        fi
    else
        echo "❌ قاعدة البيانات droobhajer_db غير موجودة"
        echo "💡 تحتاج إلى إنشاء قاعدة البيانات أولاً"
        exit 1
    fi
else
    echo "❌ فشل الاتصال بـ MySQL كـ root"
    echo ""
    echo "🔧 محاولة إصلاح صلاحيات root..."
    
    # محاولة إصلاح صلاحيات root
    echo "⚠️ سيتم إعادة تعيين صلاحيات MySQL root"
    echo "هذا قد يتطلب إعادة تشغيل MySQL"
    
    read -p "هل تريد المتابعة؟ (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "تم إلغاء العملية"
        exit 1
    fi
    
    # إيقاف MySQL
    echo "🔄 إيقاف MySQL..."
    sudo systemctl stop mysql
    
    # تشغيل MySQL في الوضع الآمن
    echo "🔄 تشغيل MySQL في الوضع الآمن..."
    sudo mysqld_safe --skip-grant-tables --skip-networking &
    MYSQL_PID=$!
    
    # انتظار تشغيل MySQL
    sleep 5
    
    # إعادة تعيين كلمة مرور root
    echo "🔄 إعادة تعيين صلاحيات root..."
    mysql -u root << EOF
USE mysql;
UPDATE user SET authentication_string = '' WHERE User = 'root';
UPDATE user SET plugin = 'mysql_native_password' WHERE User = 'root';
FLUSH PRIVILEGES;
EOF
    
    # إيقاف MySQL الآمن
    kill $MYSQL_PID 2>/dev/null
    sleep 3
    
    # إعادة تشغيل MySQL عادي
    echo "🔄 إعادة تشغيل MySQL..."
    sudo systemctl start mysql
    sleep 3
    
    # اختبار الاتصال
    if mysql -u root -e "SELECT 1;" &> /dev/null; then
        echo "✅ تم إصلاح صلاحيات root"
        
        # اختبار الاتصال من Node.js
        echo "🔄 اختبار الاتصال من Node.js..."
        if npm run test-db; then
            echo "🎉 تم إصلاح المشكلة بنجاح!"
            echo "✅ يمكنك الآن تشغيل: npm run generate-sitemap"
            exit 0
        else
            echo "❌ ما زال هناك مشكلة في الاتصال من Node.js"
        fi
    else
        echo "❌ فشل في إصلاح صلاحيات root"
    fi
fi

echo ""
echo "❌ لم يتم حل المشكلة تلقائياً"
echo ""
echo "🔧 خطوات الإصلاح اليدوي:"
echo "1. راجع ملف DATABASE_CONNECTION_TROUBLESHOOTING.md"
echo "2. تحقق من إعدادات .env.local"
echo "3. جرب إنشاء مستخدم جديد لقاعدة البيانات"
echo "4. تواصل مع الدعم الفني إذا استمرت المشكلة"
echo ""
echo "📋 معلومات مفيدة للدعم:"
echo "- إصدار MySQL: $(mysql --version)"
echo "- إصدار Node.js: $(node --version)"
echo "- نظام التشغيل: $(uname -a)"

exit 1
