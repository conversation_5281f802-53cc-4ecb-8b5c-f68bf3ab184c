import React, { useState, useRef } from 'react';
import Image from 'next/image';

interface ImageUploadWithURLProps {
  onImagesUploaded: (images: string[]) => void;
  multiple?: boolean;
  maxFiles?: number;
  currentImages?: string[];
  label?: string;
}

const ImageUploadWithURL: React.FC<ImageUploadWithURLProps> = ({
  onImagesUploaded,
  multiple = false,
  maxFiles = 1,
  currentImages = [],
  label = 'رفع الصور'
}) => {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [activeTab, setActiveTab] = useState<'upload' | 'url'>('upload');
  const [imageUrl, setImageUrl] = useState('');
  const [urlError, setUrlError] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const filesToUpload = Array.from(files);
    
    // التحقق من عدد الملفات
    if (multiple && currentImages.length + filesToUpload.length > maxFiles) {
      alert(`يمكنك رفع ${maxFiles} صور كحد أقصى`);
      return;
    }

    if (!multiple && filesToUpload.length > 1) {
      alert('يمكنك رفع صورة واحدة فقط');
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      filesToUpload.forEach((file) => {
        formData.append('files', file);
      });

      const response = await fetch('/api/upload-simple', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('فشل في رفع الصور');
      }

      const result = await response.json();
      
      if (result.success && result.files) {
        if (multiple) {
          // إضافة الصور الجديدة إلى الصور الحالية
          const newImages = [...currentImages, ...result.files];
          onImagesUploaded(newImages);
        } else {
          // استبدال الصورة الحالية
          onImagesUploaded(result.files);
        }
      } else {
        throw new Error(result.error || 'فشل في رفع الصور');
      }
    } catch (error) {
      console.error('Error uploading images:', error);
      alert('حدث خطأ أثناء رفع الصور');
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleUrlSubmit = async () => {
    if (!imageUrl.trim()) {
      setUrlError('يرجى إدخال رابط الصورة');
      return;
    }

    // التحقق من صحة الرابط
    try {
      new URL(imageUrl);
    } catch {
      setUrlError('رابط غير صحيح');
      return;
    }

    // التحقق من أن الرابط يشير إلى صورة
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
    const isImageUrl = imageExtensions.some(ext => 
      imageUrl.toLowerCase().includes(ext)
    ) || imageUrl.includes('image') || imageUrl.includes('photo') || imageUrl.includes('img');

    if (!isImageUrl) {
      // محاولة التحقق من الصورة عبر تحميلها
      try {
        const img = new window.Image();
        img.onload = () => {
          addImageUrl();
        };
        img.onerror = () => {
          setUrlError('الرابط لا يشير إلى صورة صحيحة');
        };
        img.src = imageUrl;
        return;
      } catch {
        setUrlError('الرابط لا يشير إلى صورة صحيحة');
        return;
      }
    }

    addImageUrl();
  };

  const addImageUrl = () => {
    setUrlError('');
    
    if (multiple) {
      if (currentImages.length >= maxFiles) {
        setUrlError(`يمكنك إضافة ${maxFiles} صور كحد أقصى`);
        return;
      }
      const newImages = [...currentImages, imageUrl];
      onImagesUploaded(newImages);
    } else {
      onImagesUploaded([imageUrl]);
    }
    
    setImageUrl('');
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    if (activeTab === 'upload') {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (activeTab === 'upload') {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const removeImage = (index: number) => {
    const newImages = currentImages.filter((_, i) => i !== index);
    onImagesUploaded(newImages);
  };

  const canAddMore = multiple ? currentImages.length < maxFiles : currentImages.length === 0;

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>

      {/* التبويبات */}
      {canAddMore && (
        <div className="flex border-b border-gray-200 mb-4">
          <button
            type="button"
            onClick={() => setActiveTab('upload')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'upload'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <i className="ri-upload-line ml-1"></i>
            رفع من الجهاز
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('url')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'url'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <i className="ri-link ml-1"></i>
            رابط خارجي
          </button>
        </div>
      )}

      {/* محتوى التبويب */}
      {canAddMore && (
        <div>
          {activeTab === 'upload' ? (
            // تبويب رفع الملفات
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragOver
                  ? 'border-blue-400 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple={multiple}
                onChange={(e) => handleFileSelect(e.target.files)}
                className="hidden"
              />

              {uploading ? (
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
                  <p className="text-sm text-gray-600">جاري رفع الصور...</p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <i className="ri-upload-cloud-2-line text-4xl text-gray-400 mb-2"></i>
                  <p className="text-sm text-gray-600 mb-2">
                    اسحب الصور هنا أو{' '}
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      اختر الملفات
                    </button>
                  </p>
                  <p className="text-xs text-gray-500">
                    {multiple 
                      ? `يمكنك رفع ${maxFiles - currentImages.length} صور إضافية`
                      : 'صورة واحدة فقط'
                    }
                  </p>
                </div>
              )}
            </div>
          ) : (
            // تبويب الرابط الخارجي
            <div className="border border-gray-300 rounded-lg p-4">
              <div className="space-y-3">
                <div>
                  <input
                    type="url"
                    value={imageUrl}
                    onChange={(e) => {
                      setImageUrl(e.target.value);
                      setUrlError('');
                    }}
                    placeholder="https://example.com/image.jpg"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  {urlError && (
                    <p className="text-red-500 text-xs mt-1">{urlError}</p>
                  )}
                </div>
                <button
                  type="button"
                  onClick={handleUrlSubmit}
                  disabled={!imageUrl.trim()}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-2 px-4 rounded-lg transition-colors"
                >
                  <i className="ri-add-line ml-1"></i>
                  إضافة الصورة
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* عرض الصور الحالية */}
      {currentImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {currentImages.map((image, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden border border-gray-200">
                {image.startsWith('/uploads/') || image.startsWith('/api/') || image.startsWith('http://localhost') ? (
                  <Image
                    src={image}
                    alt={`صورة ${index + 1}`}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/api/placeholder?width=200&height=200&text=خطأ في الصورة';
                    }}
                  />
                ) : (
                  // استخدام img عادي للروابط الخارجية
                  <img
                    src={image}
                    alt={`صورة ${index + 1}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/api/placeholder?width=200&height=200&text=خطأ في الصورة';
                    }}
                  />
                )}
              </div>
              <button
                type="button"
                onClick={() => removeImage(index)}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"
                title="حذف الصورة"
              >
                <i className="ri-close-line text-sm"></i>
              </button>
              {/* مؤشر نوع الصورة */}
              <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                {image.startsWith('/uploads/') ? (
                  <i className="ri-upload-line" title="مرفوعة من الجهاز"></i>
                ) : (
                  <i className="ri-link" title="رابط خارجي"></i>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* معلومات إضافية */}
      {multiple && (
        <p className="text-xs text-gray-500">
          {currentImages.length} من {maxFiles} صور
        </p>
      )}
    </div>
  );
};

export default ImageUploadWithURL;
