# 🔧 Database Connection Troubleshooting Guide
## مشروع دروب هاجر - DROOB HAJER

### المشكلة الحالية:
```
❌ Error generating sitemap: Error: Access denied for user 'root'@'localhost'
```

---

## 🔍 خطوات التشخيص:

### 1. اختبار الاتصال بقاعدة البيانات:
```bash
# تشغيل اختبار الاتصال
npm run test-db

# أو مباشرة
node scripts/test-db-connection.js
```

### 2. فحص إعدادات MySQL:
```bash
# التحقق من حالة MySQL
sudo systemctl status mysql

# إعادة تشغيل MySQL إذا لزم الأمر
sudo systemctl restart mysql

# الاتصال بـ MySQL مباشرة
mysql -u root -p
```

### 3. فحص المستخدمين والصلاحيات:
```sql
-- داخل MySQL
SELECT user, host, authentication_string FROM mysql.user WHERE user = 'root';
SHOW GRANTS FOR 'root'@'localhost';

-- التحقق من وجود قاعدة البيانات
SHOW DATABASES;
USE droobhajer_db;
SHOW TABLES;
```

---

## 🛠️ الحلول المحتملة:

### الحل 1: إعادة تعيين كلمة مرور root
```bash
# إيقاف MySQL
sudo systemctl stop mysql

# تشغيل MySQL في الوضع الآمن
sudo mysqld_safe --skip-grant-tables &

# الاتصال بدون كلمة مرور
mysql -u root

# داخل MySQL
USE mysql;
UPDATE user SET authentication_string = PASSWORD('') WHERE User = 'root';
UPDATE user SET plugin = 'mysql_native_password' WHERE User = 'root';
FLUSH PRIVILEGES;
EXIT;

# إعادة تشغيل MySQL
sudo systemctl restart mysql
```

### الحل 2: إنشاء مستخدم جديد لقاعدة البيانات
```sql
-- إنشاء مستخدم جديد
CREATE USER 'droobhajer_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON droobhajer_db.* TO 'droobhajer_user'@'localhost';
FLUSH PRIVILEGES;
```

ثم تحديث ملف `.env.local`:
```env
DB_USER=droobhajer_user
DB_PASSWORD=secure_password
```

### الحل 3: تحديث إعدادات MySQL للسماح بالاتصال بدون كلمة مرور
```bash
# تحرير ملف إعدادات MySQL
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# إضافة هذه الأسطر تحت [mysqld]
skip-grant-tables
```

⚠️ **تحذير**: هذا الحل غير آمن للإنتاج!

### الحل 4: استخدام Unix Socket
```bash
# التحقق من مكان socket
sudo find /var -name "mysqld.sock" 2>/dev/null

# عادة يكون في:
# /var/run/mysqld/mysqld.sock
# /tmp/mysql.sock
```

---

## 🔄 خطوات الإصلاح الموصى بها:

### للبيئة المحلية (Development):
1. تشغيل `npm run test-db` لتشخيص المشكلة
2. إعادة تعيين كلمة مرور root (الحل 1)
3. اختبار الاتصال مرة أخرى
4. تشغيل `npm run generate-sitemap`

### لسيرفر الإنتاج (Hostinger):
1. الاتصال بـ SSH: `ssh DROOBHAJER@**************`
2. الانتقال إلى مجلد المشروع: `cd /var/www/html`
3. تشغيل اختبار الاتصال: `npm run test-db`
4. إذا فشل، استخدام الحل 2 (إنشاء مستخدم جديد)
5. تحديث ملف `.env.local` على السيرفر
6. إعادة تشغيل التطبيق: `pm2 restart droobhajer`

---

## 📋 فحص ملفات الإعداد:

### ملف .env.local:
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=droobhajer_db
```

### ملف .env.production:
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=droobhajer_db
```

---

## 🧪 اختبار النتيجة:

بعد تطبيق أي حل:

1. **اختبار الاتصال**:
   ```bash
   npm run test-db
   ```

2. **اختبار إنشاء sitemap**:
   ```bash
   npm run generate-sitemap
   ```

3. **اختبار API**:
   ```bash
   curl http://localhost:3000/api/test-db-connection
   # أو للإنتاج
   curl http://droobhajer.com/api/test-db-connection
   ```

---

## 📞 الدعم الإضافي:

إذا استمرت المشكلة:

1. **فحص سجلات MySQL**:
   ```bash
   sudo tail -f /var/log/mysql/error.log
   ```

2. **فحص سجلات التطبيق**:
   ```bash
   pm2 logs droobhajer
   ```

3. **التحقق من إصدار MySQL**:
   ```bash
   mysql --version
   ```

4. **فحص المنافذ المفتوحة**:
   ```bash
   sudo netstat -tlnp | grep mysql
   ```

---

## ✅ علامات النجاح:

عند نجاح الإصلاح ستحصل على:
- ✅ Database connection successful
- ✅ Found X products
- ✅ Found X categories  
- ✅ Generated sitemap.xml with X URLs
- ✅ Generated robots.txt

---

**آخر تحديث**: 2024-12-30
**الحالة**: جاهز للاستخدام
