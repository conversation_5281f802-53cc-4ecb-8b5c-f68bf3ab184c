

'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../../components/admin/AdminLayout';
import ImageUploadWithURL from '../../../components/ImageUploadWithURL';
import SafeImage from '../../../components/SafeImage';
import { Category, Subcategory } from '../../../types/mysql-database';

const SubcategoriesAdmin = () => {
  const [subcategoriesList, setSubcategoriesList] = useState<Subcategory[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingSubcategory, setEditingSubcategory] = useState<Subcategory | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    categoryId: '',
    description: '',
    descriptionAr: '',
    image: '',
    isActive: true
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        // جلب الفئات الفرعية
        const subcategoriesResponse = await fetch('/api/admin/subcategories');
        if (subcategoriesResponse.ok) {
          const subcategoriesResult = await subcategoriesResponse.json();
          setSubcategoriesList(subcategoriesResult.data || []);
        }

        // جلب الفئات الرئيسية
        const categoriesResponse = await fetch('/api/admin/categories');
        if (categoriesResponse.ok) {
          const categoriesResult = await categoriesResponse.json();
          setCategories(categoriesResult.data || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredSubcategories = selectedCategory === 'all'
    ? subcategoriesList
    : subcategoriesList.filter(sub => sub.category_id === selectedCategory);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const requestData = {
        name: formData.name,
        nameAr: formData.nameAr,
        categoryId: formData.categoryId,
        description: formData.description,
        descriptionAr: formData.descriptionAr,
        image: formData.image,
        isActive: formData.isActive
      };

      if (editingSubcategory) {
        // تحديث فئة فرعية موجودة
        const response = await fetch(`/api/admin/subcategories?id=${editingSubcategory.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (response.ok) {
          const result = await response.json();
          setSubcategoriesList(prev => prev.map(sub =>
            sub.id === editingSubcategory.id ? result.data : sub
          ));
        }
      } else {
        // إضافة فئة فرعية جديدة
        const response = await fetch('/api/admin/subcategories', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (response.ok) {
          const result = await response.json();
          setSubcategoriesList(prev => [...prev, result.data]);
        }
      }

      resetForm();
    } catch (error) {
      console.error('Error saving subcategory:', error);
      alert('حدث خطأ أثناء حفظ الفئة الفرعية');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      nameAr: '',
      categoryId: '',
      description: '',
      descriptionAr: '',
      image: '',
      isActive: true
    });
    setEditingSubcategory(null);
    setShowModal(false);
  };

  const handleEdit = (subcategory: Subcategory) => {
    setEditingSubcategory(subcategory);
    setFormData({
      name: subcategory.name,
      nameAr: subcategory.name_ar,
      categoryId: subcategory.category_id,
      description: subcategory.description || '',
      descriptionAr: subcategory.description_ar || '',
      image: subcategory.image_url || '',
      isActive: subcategory.is_active
    });
    setShowModal(true);
  };

  const handleDelete = async (subcategoryId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الفئة الفرعية؟')) {
      try {
        const response = await fetch(`/api/admin/subcategories?id=${subcategoryId}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          setSubcategoriesList(prev => prev.filter(sub => sub.id !== subcategoryId));
        }
      } catch (error) {
        console.error('Error deleting subcategory:', error);
        alert('حدث خطأ أثناء حذف الفئة الفرعية');
      }
    }
  };

  const toggleStatus = async (subcategoryId: string) => {
    try {
      const subcategory = subcategoriesList.find(sub => sub.id === subcategoryId);
      if (subcategory) {
        const response = await fetch(`/api/admin/subcategories?id=${subcategoryId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            isActive: !subcategory.is_active
          }),
        });

        if (response.ok) {
          const result = await response.json();
          setSubcategoriesList(prev => prev.map(sub =>
            sub.id === subcategoryId ? result.data : sub
          ));
        }
      }
    } catch (error) {
      console.error('Error updating subcategory status:', error);
      alert('حدث خطأ أثناء تحديث حالة الفئة الفرعية');
    }
  };

  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.name_ar || categoryId;
  };

  return (
    <>
      <Head>
        <title>إدارة الفئات الفرعية - لوحة التحكم</title>
        <meta name="description" content="إدارة الفئات الفرعية للمنتجات" />
      </Head>

      <AdminLayout title="الفئات الفرعية">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">الفئات الفرعية</h1>
              <p className="text-gray-600">إدارة الفئات الفرعية للمنتجات</p>
            </div>
            <button
              onClick={() => setShowModal(true)}
              className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <i className="ri-add-line text-lg ml-2"></i>
              إضافة فئة فرعية
            </button>
          </div>

          {/* Filter */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="flex items-center space-x-6 space-x-reverse">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center ml-3">
                  <i className="ri-filter-line text-white text-lg"></i>
                </div>
                <label className="text-sm font-bold text-gray-700">فلترة حسب الفئة الرئيسية:</label>
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 bg-gradient-to-r from-gray-50 to-white"
              >
                <option value="all">جميع الفئات</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>{category.name_ar}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Subcategories Grid */}
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل الفئات الفرعية...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredSubcategories.map((subcategory) => (
              <div key={subcategory.id} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                {subcategory.image_url && (
                  <div className="h-48 bg-gray-200 overflow-hidden">
                    <SafeImage
                      src={subcategory.image_url}
                      alt={subcategory.name_ar}
                      width={400}
                      height={300}
                      className="w-full h-full object-cover"
                      fallbackText="صورة الفئة الفرعية"
                    />
                  </div>
                )}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-800 mb-1">{subcategory.name_ar}</h3>
                      <p className="text-sm text-gray-500 mb-2">{subcategory.name}</p>
                      <span className="inline-block px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                        {getCategoryName(subcategory.category_id)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        subcategory.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {subcategory.is_active ? 'نشط' : 'غير نشط'}
                      </span>
                    </div>
                  </div>

                  {subcategory.description && (
                    <p className="text-sm text-gray-600 mb-4">{subcategory.description_ar || subcategory.description}</p>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => handleEdit(subcategory)}
                        className="text-blue-600 hover:text-blue-700 p-2 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                        title="تعديل"
                      >
                        <i className="ri-edit-line text-lg"></i>
                      </button>
                      <button
                        onClick={() => toggleStatus(subcategory.id)}
                        className={`p-2 rounded-lg transition-colors duration-200 ${
                          subcategory.is_active
                            ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                            : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                        }`}
                        title={subcategory.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                      >
                        <i className={`ri-${subcategory.is_active ? 'eye-off' : 'eye'}-line text-lg`}></i>
                      </button>
                      <button
                        onClick={() => handleDelete(subcategory.id)}
                        className="text-red-600 hover:text-red-700 p-2 hover:bg-red-50 rounded-lg transition-colors duration-200"
                        title="حذف"
                      >
                        <i className="ri-delete-bin-line text-lg"></i>
                      </button>
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(subcategory.updated_at).toLocaleDateString('ar-SA')}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            </div>
          )}

          {!loading && filteredSubcategories.length === 0 && (
            <div className="text-center py-12">
              <i className="ri-folder-2-line text-6xl text-gray-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد فئات فرعية</h3>
              <p className="text-gray-600 mb-4">ابدأ بإضافة فئة فرعية جديدة</p>
              <button
                onClick={() => setShowModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors duration-200"
              >
                <i className="ri-add-line text-lg ml-2"></i>
                إضافة فئة فرعية
              </button>
            </div>
          )}
        </div>

        {/* Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-800">
                    {editingSubcategory ? 'تعديل الفئة الفرعية' : 'إضافة فئة فرعية جديدة'}
                  </h2>
                  <button
                    onClick={resetForm}
                    className="text-gray-400 hover:text-gray-600 p-2"
                  >
                    <i className="ri-close-line text-xl"></i>
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الفئة الرئيسية *
                    </label>
                    <select
                      required
                      value={formData.categoryId}
                      onChange={(e) => setFormData({...formData, categoryId: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر الفئة الرئيسية</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>{category.name_ar}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم بالعربية *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.nameAr}
                      onChange={(e) => setFormData({...formData, nameAr: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="أدخل اسم الفئة الفرعية بالعربية"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم بالإنجليزية *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter subcategory name in English"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوصف بالعربية
                    </label>
                    <textarea
                      value={formData.descriptionAr}
                      onChange={(e) => setFormData({...formData, descriptionAr: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                      placeholder="أدخل وصف الفئة الفرعية بالعربية"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوصف بالإنجليزية
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                      placeholder="Enter subcategory description in English"
                    />
                  </div>

                  <ImageUploadWithURL
                    onImagesUploaded={(images: string[]) => setFormData({...formData, image: images[0] || ''})}
                    multiple={false}
                    currentImages={formData.image ? [formData.image] : []}
                    label="صورة الفئة الفرعية"
                  />



                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isActive"
                      checked={formData.isActive}
                      onChange={(e) => setFormData({...formData, isActive: e.target.checked})}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="isActive" className="mr-2 text-sm font-medium text-gray-700">
                      فئة فرعية نشطة
                    </label>
                  </div>

                  <div className="flex space-x-3 space-x-reverse pt-4">
                    <button
                      type="submit"
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200"
                    >
                      {editingSubcategory ? 'تحديث الفئة الفرعية' : 'إضافة الفئة الفرعية'}
                    </button>
                    <button
                      type="button"
                      onClick={resetForm}
                      className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium transition-colors duration-200"
                    >
                      إلغاء
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
};

export default SubcategoriesAdmin;
